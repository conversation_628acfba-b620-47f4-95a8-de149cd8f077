.#!/usr/bin/env python3
"""
DeepSeek-V3 Development Utilities
Helpful functions and scripts for development work
"""

import os
import json
import torch
import argparse
from pathlib import Path
from typing import Dict, Any, Optional
import subprocess
import sys

class DeepSeekDevUtils:
    """Utility class for DeepSeek-V3 development"""
    
    def __init__(self, base_dir: str = "."):
        self.base_dir = Path(base_dir)
        self.inference_dir = self.base_dir / "inference"
        self.models_dir = self.base_dir / "models"
        
    def list_available_configs(self) -> Dict[str, Path]:
        """List all available model configurations"""
        configs = {}
        config_dir = self.inference_dir / "configs"
        
        if config_dir.exists():
            for config_file in config_dir.glob("config_*.json"):
                model_size = config_file.stem.replace("config_", "")
                configs[model_size] = config_file
                
        return configs
    
    def load_config(self, model_size: str = "16B") -> Dict[str, Any]:
        """Load model configuration"""
        config_path = self.inference_dir / "configs" / f"config_{model_size}.json"
        
        if not config_path.exists():
            raise FileNotFoundError(f"Config not found: {config_path}")
            
        with open(config_path, 'r') as f:
            return json.load(f)
    
    def estimate_model_size(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate model size and memory requirements"""
        vocab_size = config['vocab_size']
        dim = config['dim']
        n_layers = config['n_layers']
        n_heads = config['n_heads']
        
        # Rough parameter estimation
        embedding_params = vocab_size * dim
        
        # Attention parameters per layer
        attention_params_per_layer = (
            dim * dim * 4 +  # Q, K, V, O projections
            dim * config.get('kv_lora_rank', 512) * 2  # KV LoRA
        )
        
        # MLP parameters per layer
        mlp_params_per_layer = (
            dim * config['inter_dim'] * 2 +  # Up and down projections
            config['n_routed_experts'] * dim * config['moe_inter_dim'] * 2  # MoE experts
        )
        
        layer_params = n_layers * (attention_params_per_layer + mlp_params_per_layer)
        total_params = embedding_params + layer_params
        
        # Memory estimates (rough)
        fp32_memory_gb = total_params * 4 / (1024**3)
        fp16_memory_gb = total_params * 2 / (1024**3)
        
        return {
            "total_parameters": total_params,
            "embedding_parameters": embedding_params,
            "layer_parameters": layer_params,
            "memory_fp32_gb": fp32_memory_gb,
            "memory_fp16_gb": fp16_memory_gb,
            "estimated_inference_memory_gb": fp16_memory_gb * 1.5  # Including activations
        }
    
    def check_system_compatibility(self, model_size: str = "16B") -> Dict[str, Any]:
        """Check if system can handle the model"""
        config = self.load_config(model_size)
        size_info = self.estimate_model_size(config)
        
        # Check available memory
        try:
            import psutil
            memory = psutil.virtual_memory()
            available_memory_gb = memory.available / (1024**3)
            total_memory_gb = memory.total / (1024**3)
        except ImportError:
            available_memory_gb = None
            total_memory_gb = None
        
        # Check CUDA
        cuda_available = torch.cuda.is_available()
        cuda_devices = torch.cuda.device_count() if cuda_available else 0
        
        compatibility = {
            "model_size": model_size,
            "estimated_memory_needed": size_info["estimated_inference_memory_gb"],
            "system_memory_total": total_memory_gb,
            "system_memory_available": available_memory_gb,
            "cuda_available": cuda_available,
            "cuda_devices": cuda_devices,
            "can_run_cpu": available_memory_gb and available_memory_gb > size_info["estimated_inference_memory_gb"] if available_memory_gb else None,
            "recommended_setup": self._get_recommended_setup(size_info, available_memory_gb, cuda_available)
        }
        
        return compatibility
    
    def _get_recommended_setup(self, size_info: Dict, available_memory: Optional[float], cuda_available: bool) -> str:
        """Get recommended setup based on system capabilities"""
        memory_needed = size_info["estimated_inference_memory_gb"]
        
        if not available_memory:
            return "Cannot determine - install psutil for memory checking"
        
        if cuda_available:
            if available_memory > memory_needed:
                return "GPU inference recommended"
            else:
                return "GPU available but insufficient memory - consider smaller model"
        else:
            if available_memory > memory_needed:
                return "CPU inference possible (will be slow)"
            else:
                return "Insufficient memory - use smaller model or cloud inference"
    
    def start_jupyter(self, notebook: str = "deepseek_v3_dev.ipynb"):
        """Start Jupyter Lab with the development notebook"""
        notebook_path = self.base_dir / notebook
        
        if not notebook_path.exists():
            print(f"Notebook not found: {notebook_path}")
            return False
        
        try:
            cmd = ["jupyter", "lab", str(notebook_path)]
            print(f"Starting Jupyter Lab: {' '.join(cmd)}")
            subprocess.run(cmd, cwd=self.base_dir)
            return True
        except FileNotFoundError:
            print("Jupyter Lab not found. Install with: pip install jupyterlab")
            return False
        except KeyboardInterrupt:
            print("\nJupyter Lab stopped.")
            return True
    
    def create_simple_inference_script(self, model_size: str = "16B"):
        """Create a simple inference script template"""
        script_content = f'''#!/usr/bin/env python3
"""
Simple DeepSeek-V3 Inference Script
Model size: {model_size}
"""

import sys
import os
import json
import torch

# Add inference directory to path
sys.path.append('inference')

def main():
    print("DeepSeek-V3 Simple Inference")
    print("=" * 40)
    
    # Load configuration
    config_path = 'inference/configs/config_{model_size}.json'
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    print(f"Model: {model_size}")
    print(f"Parameters: ~{{config['vocab_size'] * config['dim']:,}}")
    
    # Note: This is a template - actual inference requires model weights
    print("\\nNote: This is a template script.")
    print("For actual inference, you need:")
    print("1. Model weights downloaded")
    print("2. Proper tokenizer setup")
    print("3. Inference framework (SGLang, vLLM, etc.)")
    
    return True

if __name__ == "__main__":
    main()
'''
        
        script_path = self.base_dir / f"simple_inference_{model_size.lower()}.py"
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        # Make executable
        os.chmod(script_path, 0o755)
        print(f"Created: {script_path}")
        
        return script_path

def main():
    parser = argparse.ArgumentParser(description="DeepSeek-V3 Development Utilities")
    parser.add_argument("command", choices=["info", "check", "jupyter", "create-script"],
                       help="Command to run")
    parser.add_argument("--model-size", default="16B", 
                       help="Model size (default: 16B)")
    
    args = parser.parse_args()
    
    utils = DeepSeekDevUtils()
    
    if args.command == "info":
        print("📋 Available Configurations:")
        configs = utils.list_available_configs()
        for size, path in configs.items():
            print(f"  {size}: {path}")
        
        print(f"\\n📊 Model Info ({args.model_size}):")
        try:
            config = utils.load_config(args.model_size)
            size_info = utils.estimate_model_size(config)
            
            print(f"  Parameters: {size_info['total_parameters']:,}")
            print(f"  Memory (FP16): {size_info['memory_fp16_gb']:.1f} GB")
            print(f"  Inference Memory: {size_info['estimated_inference_memory_gb']:.1f} GB")
        except Exception as e:
            print(f"  Error: {e}")
    
    elif args.command == "check":
        print("🔍 System Compatibility Check:")
        try:
            compat = utils.check_system_compatibility(args.model_size)
            
            print(f"  Model: {compat['model_size']}")
            print(f"  Memory needed: {compat['estimated_memory_needed']:.1f} GB")
            print(f"  System memory: {compat['system_memory_total']:.1f} GB")
            print(f"  Available memory: {compat['system_memory_available']:.1f} GB")
            print(f"  CUDA available: {compat['cuda_available']}")
            print(f"  Recommendation: {compat['recommended_setup']}")
        except Exception as e:
            print(f"  Error: {e}")
    
    elif args.command == "jupyter":
        utils.start_jupyter()
    
    elif args.command == "create-script":
        script_path = utils.create_simple_inference_script(args.model_size)
        print(f"✅ Created inference script: {script_path}")

if __name__ == "__main__":
    main()
