{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# DeepSeek-V3 Development Notebook\n", "This notebook helps you get started with DeepSeek-V3 development."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append('inference')\n", "\n", "import torch\n", "import json\n", "from model import Transformer, ModelArgs\n", "from transformers import AutoTokenizer\n", "\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load model configuration\n", "with open('inference/configs/config_16B.json', 'r') as f:\n", "    config = json.load(f)\n", "\n", "print(\"Model configuration:\")\n", "for key, value in config.items():\n", "    print(f\"  {key}: {value}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}