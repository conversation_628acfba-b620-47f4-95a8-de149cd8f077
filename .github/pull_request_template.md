# Pull Request Template

## 📋 Beschrijving
Geef een duidelijke en beknopte beschrijving van de wijzigingen in deze PR.

### Type wijziging
- [ ] Bug fix (non-breaking change die een issue oplost)
- [ ] Nieuwe feature (non-breaking change die functionaliteit toevoegt)
- [ ] Breaking change (fix of feature die bestaande functionaliteit breekt)
- [ ] Documentatie update
- [ ] Performance verbetering
- [ ] Code refactoring
- [ ] Test verbetering

## 🔗 Gerelateerde Issues
Fixes #(issue nummer)
Relates to #(issue nummer)

## 🧪 Testing
Beschrijf de tests die je hebt uitgevoerd om je wijzigingen te verifiëren.

### Test Configuratie
- [ ] Unit tests toegevoegd/bijgewerkt
- [ ] Integration tests toegevoegd/bijgewerkt
- [ ] Performance tests uitgevoerd
- [ ] Manual testing uitgevoerd

### Test Resultaten
```
Voeg hier test output toe indien relevant
```

## 📸 Screenshots (indien van toepassing)
Voeg screenshots toe voor UI wijzigingen.

## ✅ Checklist
Zorg ervoor dat alle items zijn afgevinkt voordat je de PR indient:

### Code Quality
- [ ] Code volgt de project style guide
- [ ] Self-review van eigen code uitgevoerd
- [ ] Code is gecommenteerd, vooral in complexe gebieden
- [ ] Geen console.log/print statements achtergelaten
- [ ] Geen hardcoded waarden (gebruik configuratie)

### Testing
- [ ] Nieuwe tests toegevoegd voor nieuwe functionaliteit
- [ ] Alle tests slagen lokaal
- [ ] Test coverage is behouden of verbeterd
- [ ] Edge cases zijn getest

### Documentation
- [ ] Documentatie bijgewerkt waar nodig
- [ ] README bijgewerkt indien van toepassing
- [ ] API documentatie bijgewerkt
- [ ] Changelog entry toegevoegd

### Security
- [ ] Geen gevoelige informatie in code
- [ ] Input validatie geïmplementeerd
- [ ] Security best practices gevolgd
- [ ] Dependencies zijn up-to-date

### Performance
- [ ] Geen performance regressies geïntroduceerd
- [ ] Memory leaks gecontroleerd
- [ ] Database queries geoptimaliseerd
- [ ] Caching overwogen waar relevant

## 🔍 Review Opmerkingen
Specifieke gebieden waar je feedback op wilt:
- [ ] Architectuur beslissingen
- [ ] Performance implicaties
- [ ] Security overwegingen
- [ ] User experience
- [ ] Code organisatie

## 📝 Deployment Opmerkingen
Speciale instructies voor deployment:
- [ ] Database migraties vereist
- [ ] Configuratie wijzigingen nodig
- [ ] Environment variabelen toegevoegd
- [ ] Dependencies bijgewerkt
- [ ] Backward compatibility overwegingen

## 🚀 Post-Deployment
Acties na deployment:
- [ ] Monitoring alerts configureren
- [ ] Performance metrics controleren
- [ ] User acceptance testing
- [ ] Documentation updates publiceren

---

**Reviewer Guidelines:**
- Controleer alle checklist items
- Test de wijzigingen lokaal indien mogelijk
- Geef constructieve feedback
- Approve alleen als alle criteria zijn voldaan
