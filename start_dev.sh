#!/bin/bash

# DeepSeek-V3 Development Environment Quick Start
# This script helps you get started with DeepSeek-V3 development

set -e

echo "🚀 DeepSeek-V3 Development Environment"
echo "======================================"

# Check if virtual environment exists
if [ ! -d "deepseek_env" ]; then
    echo "❌ Virtual environment not found!"
    echo "Please run the setup first."
    exit 1
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source deepseek_env/bin/activate

# Check if setup is complete
if [ ! -f "test_model.py" ]; then
    echo "📥 Setting up development environment..."
    python dev_setup.py --model-size 16B
fi

# Run system check
echo ""
echo "🔍 Checking system compatibility..."
python dev_utils.py check --model-size 16B

echo ""
echo "📋 Available commands:"
echo "  python test_model.py              - Test the setup"
echo "  python dev_utils.py info          - Show model information"
echo "  python dev_utils.py check         - Check system compatibility"
echo "  python dev_utils.py jupyter       - Start Jupyter Lab"
echo "  jupyter lab deepseek_v3_dev.ipynb - Start development notebook"
echo ""

# Ask user what they want to do
echo "What would you like to do?"
echo "1) Run tests"
echo "2) Start Jupyter Lab"
echo "3) Show model info"
echo "4) Exit"
echo ""

read -p "Choose an option (1-4): " choice

case $choice in
    1)
        echo "🧪 Running tests..."
        python test_model.py
        ;;
    2)
        echo "📓 Starting Jupyter Lab..."
        python dev_utils.py jupyter
        ;;
    3)
        echo "📊 Model information..."
        python dev_utils.py info
        ;;
    4)
        echo "👋 Goodbye!"
        exit 0
        ;;
    *)
        echo "Invalid option. Exiting."
        exit 1
        ;;
esac

echo ""
echo "🎉 Development environment ready!"
echo "Check README_DEV.md for more information."
