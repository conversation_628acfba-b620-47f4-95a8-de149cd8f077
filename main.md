# DeepSeek-V3 Project Regels & Richtlijnen

## 📋 Overzicht
Dit document bevat alle regels, conventies en richtlijnen voor het DeepSeek-V3 project. Alle teamleden en AI-assistenten moeten deze regels volgen.

## 🎯 Kernprincipes

### 1. Proactieve Aanpak
- **Altijd proactief zijn** - Anticipeer op behoeften en problemen
- **Denk vooruit** - Plan minimaal 2-3 stappen vooruit
- **Stel vragen** - Als iets onduidelijk is, vraag om verduidelijking
- **Documenteer alles** - Leg beslissingen en redeneringen vast

### 2. Automatisering & Efficiëntie
- **Automatiseer repetitieve taken** waar mogelijk
- **Gebruik scripts** voor standaard procedures
- **Minimaliseer handmatige interventie**
- **Optimaliseer workflows** continu

### 3. Kwaliteit & Consistentie
- **Code reviews** zijn verplicht voor alle wijzigingen
- **Testing** is verplicht voordat code wordt gemerged
- **Documentatie** moet up-to-date blijven
- **Consistente naamgeving** en code stijl

## 🔧 Development Regels

### Code Standaarden
```python
# Gebruik altijd type hints
def process_model(model_path: str, config: Dict[str, Any]) -> ModelResult:
    """Verwerk model met gegeven configuratie."""
    pass

# Gebruik descriptieve variabele namen
model_inference_result = run_inference(input_data)
# NIET: result = run(data)
```

### Git Workflow
1. **Branch naming**: `feature/beschrijving` of `fix/probleem-beschrijving`
2. **Commit messages**: Gebruik Nederlandse beschrijvingen
   - `feat: voeg nieuwe inference functie toe`
   - `fix: los memory leak in model loading op`
   - `docs: update README met nieuwe instructies`
3. **Pull Requests**: Minimaal 1 reviewer vereist

### Testing Vereisten
- **Unit tests** voor alle nieuwe functies
- **Integration tests** voor API endpoints
- **Performance tests** voor model inference
- **Minimaal 80% code coverage**

## 🤖 AI Assistant Regels

### Taakuitvoering
1. **Begrijp de volledige context** voordat je begint
2. **Maak een plan** en deel dit met de gebruiker
3. **Voer taken stap voor stap uit**
4. **Documenteer je acties** en beslissingen
5. **Test je implementatie** voordat je het als compleet markeert

### Communicatie
- **Gebruik Nederlands** tenzij anders gevraagd
- **Wees specifiek** in je antwoorden
- **Leg complexe concepten uit** in begrijpelijke taal
- **Vraag om feedback** bij onzekerheid

### Code Wijzigingen
- **Gebruik altijd codebase-retrieval** voordat je wijzigingen maakt
- **Respecteer bestaande architectuur** en patronen
- **Maak incrementele wijzigingen** waar mogelijk
- **Test wijzigingen** voordat je ze committeert

## 📁 Project Structuur

### Directory Layout
```
DeepSeek-V3/
├── main.md                 # Dit document
├── README.md              # Project overzicht
├── inference/             # Inference code en scripts
├── models/                # Model bestanden
├── tests/                 # Test bestanden
├── docs/                  # Documentatie
├── scripts/               # Utility scripts
└── configs/               # Configuratie bestanden
```

### Bestandsnaamgeving
- **Python bestanden**: `snake_case.py`
- **Configuratie bestanden**: `config_naam.json` of `config_naam.yaml`
- **Scripts**: `actie_beschrijving.sh`
- **Documentatie**: `ONDERWERP.md` (hoofdletters voor belangrijke docs)

## 🔄 MCP (Model Context Protocol) Configuratie

### Setup Vereisten
1. **Context management** voor AI interacties
2. **Automatische documentatie** van beslissingen
3. **Workflow tracking** en logging
4. **Integration** met development tools

### Implementatie Stappen
- [ ] Installeer MCP dependencies
- [ ] Configureer context providers
- [ ] Setup logging en monitoring
- [ ] Test integratie met AI tools

## ⚡ Automatische Taakuitvoering

### Triggers
- **Code push**: Automatische tests en linting
- **PR creation**: Code review checklist
- **Issue creation**: Automatische labeling en assignment
- **Release**: Automatische deployment en documentatie update

### Scripts Locatie
Alle automatisering scripts in `scripts/` directory:
- `scripts/setup_env.sh` - Environment setup
- `scripts/run_tests.sh` - Test uitvoering
- `scripts/deploy.sh` - Deployment
- `scripts/update_docs.sh` - Documentatie update

## 🚀 Deployment & Release

### Staging Process
1. **Development** → lokale testing
2. **Staging** → integration testing
3. **Production** → live deployment

### Release Checklist
- [ ] Alle tests slagen
- [ ] Documentatie is bijgewerkt
- [ ] Performance benchmarks zijn uitgevoerd
- [ ] Security scan is uitgevoerd
- [ ] Backup is gemaakt

## 📊 Monitoring & Logging

### Metrics
- **Model performance**: Latency, throughput, accuracy
- **System resources**: CPU, GPU, memory usage
- **Error rates**: Failed requests, exceptions
- **User metrics**: Request patterns, usage statistics

### Logging Levels
- **DEBUG**: Gedetailleerde informatie voor debugging
- **INFO**: Algemene informatie over operaties
- **WARNING**: Potentiële problemen
- **ERROR**: Fouten die aandacht vereisen
- **CRITICAL**: Kritieke fouten die onmiddellijke actie vereisen

## 🔒 Security & Privacy

### Data Handling
- **Geen gevoelige data** in logs
- **Encryptie** voor data in transit en at rest
- **Access controls** voor model bestanden
- **Regular security audits**

### API Security
- **Rate limiting** op alle endpoints
- **Authentication** vereist voor alle API calls
- **Input validation** en sanitization
- **HTTPS only** voor alle communicatie

## 📞 Support & Escalatie

### Contact Informatie
- **Technical Lead**: [Naam] - [Email]
- **DevOps**: [Naam] - [Email]
- **Security**: [Naam] - [Email]

### Escalatie Procedure
1. **Level 1**: Lokale troubleshooting (30 min)
2. **Level 2**: Team lead consultatie (1 uur)
3. **Level 3**: Senior engineer involvement (2 uur)
4. **Level 4**: Management escalatie (onmiddellijk)

---

**Laatste update**: 2025-06-28
**Versie**: 1.0
**Eigenaar**: DeepSeek-V3 Development Team
