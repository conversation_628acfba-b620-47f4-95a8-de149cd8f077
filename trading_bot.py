#!/usr/bin/env python3
"""
🤖 DeepSeek-V3 AI Trading Bot voor Leon
Automated trading met AI-powered marktanalyse
"""
import json
import logging
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional

import ccxt
import pandas as pd
import requests

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class DeepSeekAPI:
    """DeepSeek-V3 API interface voor trading analyse"""

    def __init__(self):
        self.api_key = os.getenv("DEEPSEEK_API_KEY")
        self.base_url = "https://api.deepseek.com"

    def analyze_market(self, market_data: Dict) -> str:
        """Krijg AI-analyse van marktdata"""
        prompt = f"""
        Als professionele crypto trading analist, analyseer deze marktdata:
        
        📊 MARKTDATA:
        Symbol: {market_data['symbol']}
        Huidige prijs: ${market_data['price']:,.2f}
        24h change: {market_data['change_24h']:+.2f}%
        Volume: ${market_data['volume']:,.0f}
        RSI: {market_data.get('rsi', 'N/A')}
        
        📈 TECHNISCHE INDICATOREN:
        Moving Average 20: {market_data.get('ma20', 'N/A')}
        Moving Average 50: {market_data.get('ma50', 'N/A')}
        Bollinger Bands: {market_data.get('bb_upper', 'N/A')} / {market_data.get('bb_lower', 'N/A')}
        
        Geef een trading advies in dit formaat:
        ACTIE: [BUY/SELL/HOLD]
        VERTROUWEN: [1-10]
        REDEN: [korte technische analyse]
        ENTRY_PRICE: [als buy/sell]
        STOP_LOSS: [risico management]
        TAKE_PROFIT: [target price]
        """

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        data = {
            "model": "deepseek-chat",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.3,  # Lager voor consistentere trading adviezen
        }

        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30,
            )
            response.raise_for_status()
            return response.json()["choices"][0]["message"]["content"]
        except Exception as e:
            logger.error(f"DeepSeek API fout: {e}")
            return f"Error: {e}"


class TechnicalAnalysis:
    """Technische analyse tools"""

    @staticmethod
    def calculate_rsi(prices: List[float], period: int = 14) -> float:
        """Bereken RSI indicator"""
        if len(prices) < period + 1:
            return 50.0

        deltas = [prices[i] - prices[i - 1] for i in range(1, len(prices))]
        gains = [d if d > 0 else 0 for d in deltas]
        losses = [-d if d < 0 else 0 for d in deltas]

        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period

        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return round(rsi, 2)

    @staticmethod
    def calculate_ma(prices: List[float], period: int) -> float:
        """Bereken moving average"""
        if len(prices) < period:
            return prices[-1] if prices else 0.0
        return sum(prices[-period:]) / period

    @staticmethod
    def calculate_bollinger_bands(prices: List[float], period: int = 20) -> Dict:
        """Bereken Bollinger Bands"""
        if len(prices) < period:
            price = prices[-1] if prices else 0.0
            return {"upper": price, "middle": price, "lower": price}

        ma = sum(prices[-period:]) / period
        variance = sum([(p - ma) ** 2 for p in prices[-period:]]) / period
        std_dev = variance**0.5

        return {"upper": ma + (2 * std_dev), "middle": ma, "lower": ma - (2 * std_dev)}


class TradingBot:
    """Main trading bot class"""

    def __init__(self):
        self.deepseek = DeepSeekAPI()
        self.ta = TechnicalAnalysis()
        self.exchanges = {}
        self.positions = {}
        self.trade_history = []
        self.config = self.load_config()

        # Setup exchanges
        self.setup_exchanges()

    def load_config(self) -> Dict:
        """Laad trading configuratie"""
        default_config = {
            "symbols": ["BTC/USDT", "ETH/USDT", "ADA/USDT", "SOL/USDT"],
            "max_position_size": 1000,  # USD
            "stop_loss_pct": 2.0,  # 2%
            "take_profit_pct": 5.0,  # 5%
            "min_confidence": 7,  # Minimum AI confidence (1-10)
            "check_interval": 300,  # 5 minuten
            "paper_trading": True,  # Start in demo mode
        }

        config_file = "bot_config.json"
        if os.path.exists(config_file):
            with open(config_file, "r") as f:
                config = json.load(f)
                # Merge met defaults
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
        else:
            # Maak default config
            with open(config_file, "w") as f:
                json.dump(default_config, f, indent=2)
            return default_config

    def setup_exchanges(self):
        """Setup exchange connecties"""
        # Binance (paper trading)
        self.exchanges["binance"] = ccxt.binance(
            {
                "apiKey": os.getenv("BINANCE_API_KEY", ""),
                "secret": os.getenv("BINANCE_SECRET", ""),
                "sandbox": self.config.get("paper_trading", True),
                "enableRateLimit": True,
            }
        )

        logger.info(
            f"📡 Exchange setup - Paper trading: {self.config['paper_trading']}"
        )

    def get_market_data(self, symbol: str) -> Dict:
        """Haal uitgebreide marktdata op"""
        try:
            exchange = self.exchanges["binance"]

            # Basis ticker data
            ticker = exchange.fetch_ticker(symbol)

            # OHLCV data voor technische analyse
            ohlcv = exchange.fetch_ohlcv(symbol, "1h", limit=100)
            closes = [candle[4] for candle in ohlcv]  # Closing prices

            # Bereken technische indicatoren
            rsi = self.ta.calculate_rsi(closes)
            ma20 = self.ta.calculate_ma(closes, 20)
            ma50 = self.ta.calculate_ma(closes, 50)
            bb = self.ta.calculate_bollinger_bands(closes)

            return {
                "symbol": symbol,
                "price": ticker["last"],
                "change_24h": ticker["percentage"],
                "volume": ticker["baseVolume"],
                "rsi": rsi,
                "ma20": ma20,
                "ma50": ma50,
                "bb_upper": bb["upper"],
                "bb_lower": bb["lower"],
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Fout bij ophalen marktdata voor {symbol}: {e}")
            return {}

    def parse_ai_signal(self, ai_response: str) -> Dict:
        """Parse AI trading signaal"""
        signal = {
            "action": "HOLD",
            "confidence": 5,
            "reason": "Geen duidelijk signaal",
            "entry_price": None,
            "stop_loss": None,
            "take_profit": None,
        }

        lines = ai_response.split("\n")
        for line in lines:
            line = line.strip()
            if line.startswith("ACTIE:"):
                action = line.split(":")[1].strip().upper()
                if action in ["BUY", "SELL", "HOLD"]:
                    signal["action"] = action
            elif line.startswith("VERTROUWEN:"):
                try:
                    signal["confidence"] = int(line.split(":")[1].strip())
                except:
                    pass
            elif line.startswith("REDEN:"):
                signal["reason"] = line.split(":", 1)[1].strip()
            elif line.startswith("ENTRY_PRICE:"):
                try:
                    signal["entry_price"] = float(
                        line.split(":")[1].strip().replace("$", "").replace(",", "")
                    )
                except:
                    pass

        return signal

    def execute_trade(self, symbol: str, signal: Dict, market_data: Dict):
        """Voer trade uit (of simuleer in paper trading)"""
        if signal["confidence"] < self.config["min_confidence"]:
            logger.info(
                f"❌ {symbol}: Confidence te laag ({signal['confidence']}) - Skip"
            )
            return

        if signal["action"] == "HOLD":
            logger.info(f"⏸️  {symbol}: HOLD signaal")
            return

        # Bereken position size
        position_size = min(
            self.config["max_position_size"],
            self.config["max_position_size"] * (signal["confidence"] / 10),
        )

        current_price = market_data["price"]

        if self.config["paper_trading"]:
            # Paper trading - simuleer trade
            trade = {
                "symbol": symbol,
                "action": signal["action"],
                "price": current_price,
                "size": position_size,
                "confidence": signal["confidence"],
                "reason": signal["reason"],
                "timestamp": datetime.now().isoformat(),
                "paper_trade": True,
            }

            self.trade_history.append(trade)
            self.positions[symbol] = trade

            logger.info(
                f"📝 PAPER TRADE: {signal['action']} {symbol} @ ${current_price:,.2f}"
            )
            logger.info(
                f"   💰 Size: ${position_size:,.2f} | Confidence: {signal['confidence']}/10"
            )
            logger.info(f"   📖 Reden: {signal['reason']}")
        else:
            # Real trading - implementeer hier je echte trading logica
            logger.warning(
                "🚨 REAL TRADING NIET GEÏMPLEMENTEERD - Gebruik paper_trading: true"
            )

    def run_analysis_cycle(self):
        """Run één volledige analyse cyclus"""
        logger.info("🔄 Start analyse cyclus...")

        for symbol in self.config["symbols"]:
            try:
                # Haal marktdata op
                market_data = self.get_market_data(symbol)
                if not market_data:
                    continue

                # Krijg AI analyse
                logger.info(f"🤖 Analyseer {symbol}...")
                ai_response = self.deepseek.analyze_market(market_data)

                # Parse signaal
                signal = self.parse_ai_signal(ai_response)

                # Log resultaten
                logger.info(
                    f"📊 {symbol}: {signal['action']} (Confidence: {signal['confidence']}/10)"
                )
                logger.info(f"   💭 {signal['reason']}")

                # Execute trade
                self.execute_trade(symbol, signal, market_data)

                # Kleine pauze tussen symbolen
                time.sleep(2)

            except Exception as e:
                logger.error(f"Fout bij analyse {symbol}: {e}")

    def show_status(self):
        """Toon huidige status"""
        print("\n" + "=" * 60)
        print("🤖 DeepSeek-V3 Trading Bot Status")
        print("=" * 60)

        print(f"📊 Actieve posities: {len(self.positions)}")
        print(
            f"📈 Trades vandaag: {len([t for t in self.trade_history if t['timestamp'].startswith(datetime.now().strftime('%Y-%m-%d'))])}"
        )
        print(f"💰 Paper trading: {'✅' if self.config['paper_trading'] else '❌'}")

        if self.positions:
            print("\n🎯 Open posities:")
            for symbol, pos in self.positions.items():
                print(f"   {symbol}: {pos['action']} @ ${pos['price']:,.2f}")

        print(
            f"\n⏰ Volgende check: {(datetime.now() + timedelta(seconds=self.config['check_interval'])).strftime('%H:%M:%S')}"
        )

    def start(self):
        """Start de trading bot"""
        logger.info("🚀 DeepSeek-V3 Trading Bot gestart!")

        if not self.deepseek.api_key:
            logger.error("❌ DEEPSEEK_API_KEY niet gevonden!")
            print("\n💡 Setup instructies:")
            print("1. Ga naar platform.deepseek.com")
            print("2. Maak account en krijg API key")
            print("3. export DEEPSEEK_API_KEY='your_key_here'")
            return

        try:
            while True:
                self.run_analysis_cycle()
                self.show_status()

                # Wacht tot volgende cyclus
                logger.info(f"⏰ Wacht {self.config['check_interval']} seconden...")
                time.sleep(self.config["check_interval"])

        except KeyboardInterrupt:
            logger.info("\n👋 Bot gestopt door gebruiker")
        except Exception as e:
            logger.error(f"💥 Onverwachte fout: {e}")


def setup_environment():
    """Setup trading environment"""
    print("🔧 Trading Bot Setup")
    print("=" * 30)

    # Check API keys
    deepseek_key = os.getenv("DEEPSEEK_API_KEY")
    binance_key = os.getenv("BINANCE_API_KEY")

    print(f"DeepSeek API: {'✅' if deepseek_key else '❌'}")
    print(
        f"Binance API: {'✅' if binance_key else '⚠️  (Optioneel voor paper trading)'}"
    )

    if not deepseek_key:
        print("\n💡 DeepSeek API Key needed:")
        print("export DEEPSEEK_API_KEY='your_key_here'")
        return False

    return True


if __name__ == "__main__":
    print("🤖 DeepSeek-V3 AI Trading Bot voor Leon")
    print("=" * 50)

    if not setup_environment():
        exit(1)

    print("\nKies een optie:")
    print("1. Start trading bot")
    print("2. Test AI analyse")
    print("3. Configuratie aanpassen")
    print("4. Trade history bekijken")

    choice = input("\nKeuze (1-4): ").strip()

    if choice == "1":
        bot = TradingBot()
        bot.start()
    elif choice == "2":
        # Test AI analyse
        deepseek = DeepSeekAPI()
        test_data = {
            "symbol": "BTC/USDT",
            "price": 42000.50,
            "change_24h": 2.5,
            "volume": 1500000,
            "rsi": 65.2,
        }
        print("\n🧪 Test AI analyse...")
        result = deepseek.analyze_market(test_data)
        print(f"\n🤖 AI Response:\n{result}")
    elif choice == "3":
        print("\n⚙️ Edit bot_config.json voor configuratie")
    elif choice == "4":
        print("\n📈 Trade history feature komt binnenkort...")
    else:
        print("❌ Ongeldige keuze")
