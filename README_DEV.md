# DeepSeek-V3 Development Environment

This development environment provides tools and utilities for working with DeepSeek-V3 models.

## 🚀 Quick Start

### 1. Environment Setup
```bash
# Activate the virtual environment
source deepseek_env/bin/activate

# Test the setup
python test_model.py
```

### 2. Development Tools

#### Check System Compatibility
```bash
python dev_utils.py check --model-size 16B
```

#### Get Model Information
```bash
python dev_utils.py info --model-size 16B
```

#### Start Jupyter Development Environment
```bash
python dev_utils.py jupyter
```

#### Create Inference Script Template
```bash
python dev_utils.py create-script --model-size 16B
```

## 📁 Project Structure

```
DeepSeek-V3/
├── deepseek_env/              # Python virtual environment
├── inference/                 # Core inference code
│   ├── configs/              # Model configurations
│   ├── model.py              # Model implementation
│   ├── generate.py           # Generation script
│   └── requirements.txt      # Dependencies
├── models/                   # Downloaded model files
├── dev_setup.py             # Development setup script
├── dev_utils.py             # Development utilities
├── test_model.py            # Test script
├── deepseek_v3_dev.ipynb    # Jupyter development notebook
└── README_DEV.md            # This file
```

## 🔧 Available Model Sizes

- **16B**: Smallest version, good for development and testing
- **236B**: Medium version (DeepSeek-V2 size)
- **671B**: Full DeepSeek-V3 version

## 💻 System Requirements

### Minimum Requirements (16B model)
- **RAM**: 8+ GB available
- **Storage**: 50+ GB free space
- **Python**: 3.10+
- **PyTorch**: 2.6.0+

### Recommended for Development
- **RAM**: 16+ GB
- **GPU**: NVIDIA GPU with 8+ GB VRAM (optional, CPU works too)
- **Storage**: 100+ GB for model weights

### Production Requirements (671B model)
- **RAM**: 64+ GB
- **GPU**: Multiple high-end GPUs or cloud deployment
- **Storage**: 1.5+ TB

## 🛠️ Development Workflow

### 1. Model Configuration
```python
from dev_utils import DeepSeekDevUtils

utils = DeepSeekDevUtils()
config = utils.load_config("16B")
size_info = utils.estimate_model_size(config)
print(f"Parameters: {size_info['total_parameters']:,}")
```

### 2. System Compatibility Check
```python
compat = utils.check_system_compatibility("16B")
print(f"Can run: {compat['recommended_setup']}")
```

### 3. Jupyter Development
- Open `deepseek_v3_dev.ipynb` in Jupyter Lab
- Experiment with model configurations
- Test inference code snippets

## 📚 Key Files

### `dev_setup.py`
- Sets up the development environment
- Downloads model configurations
- Creates development scripts

### `dev_utils.py`
- Utility functions for development
- System compatibility checking
- Model size estimation

### `test_model.py`
- Tests basic functionality
- Validates environment setup
- Works without GPU/Triton

### `deepseek_v3_dev.ipynb`
- Interactive development notebook
- Code examples and experiments
- Model exploration

## 🚨 Important Notes

### macOS Limitations
- **Triton**: Not available on macOS (GPU kernels won't work)
- **CUDA**: Not available on macOS
- **Inference**: CPU-only, will be slower
- **Recommendation**: Use for development, deploy on Linux for production

### Memory Considerations
- **16B model**: ~3-4 GB RAM minimum
- **236B model**: ~15-20 GB RAM minimum  
- **671B model**: ~40+ GB RAM minimum
- **Add 50% for inference overhead**

### Production Deployment
For production inference, consider:
- **SGLang**: High-performance inference framework
- **vLLM**: Efficient serving with batching
- **LMDeploy**: Flexible deployment options
- **Cloud services**: For large models

## 🔍 Troubleshooting

### Common Issues

#### "ModuleNotFoundError: No module named 'triton'"
- **Cause**: Triton not available on macOS
- **Solution**: Use CPU inference or Linux system

#### "CUDA not available"
- **Cause**: No NVIDIA GPU or drivers
- **Solution**: Use CPU inference (slower)

#### "Insufficient memory"
- **Cause**: Model too large for system
- **Solution**: Use smaller model size

#### "Config file not found"
- **Cause**: Model configuration not downloaded
- **Solution**: Run `python dev_setup.py`

### Getting Help

1. Check system compatibility: `python dev_utils.py check`
2. Verify setup: `python test_model.py`
3. Review logs for specific error messages
4. Consider using cloud deployment for large models

## 🎯 Next Steps

### For Development
1. Experiment with model configurations
2. Test inference code patterns
3. Develop custom applications

### For Production
1. Set up Linux environment with GPU
2. Install production inference framework
3. Download full model weights
4. Configure serving infrastructure

## 📖 Additional Resources

- [DeepSeek-V3 Paper](https://arxiv.org/pdf/2412.19437)
- [Hugging Face Model](https://huggingface.co/deepseek-ai/DeepSeek-V3)
- [SGLang Documentation](https://github.com/sgl-project/sglang)
- [vLLM Documentation](https://docs.vllm.ai/)

---

**Happy coding with DeepSeek-V3! 🚀**
