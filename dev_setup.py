#!/usr/bin/env python3
"""
DeepSeek-V3 Development Setup Script
This script helps set up the development environment for DeepSeek-V3
"""

import os
import sys
import json
import argparse
from pathlib import Path
from huggingface_hub import snapshot_download, hf_hub_download
import torch

def check_system_requirements():
    """Check if system meets minimum requirements"""
    print("🔍 Checking system requirements...")
    
    # Check Python version
    python_version = sys.version_info
    print(f"✅ Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Check PyTorch
    print(f"✅ PyTorch version: {torch.__version__}")
    
    # Check CUDA availability
    if torch.cuda.is_available():
        print(f"✅ CUDA available: {torch.cuda.device_count()} devices")
        for i in range(torch.cuda.device_count()):
            print(f"   - Device {i}: {torch.cuda.get_device_name(i)}")
    else:
        print("⚠️  CUDA not available - will use CPU (slower)")
    
    # Check available memory
    import psutil
    memory = psutil.virtual_memory()
    print(f"💾 Available RAM: {memory.available / (1024**3):.1f} GB / {memory.total / (1024**3):.1f} GB")
    
    # Check disk space
    disk = psutil.disk_usage('.')
    print(f"💽 Available disk space: {disk.free / (1024**3):.1f} GB")
    
    return True

def download_model_config(model_size="16B"):
    """Download model configuration"""
    print(f"📥 Downloading {model_size} model configuration...")
    
    config_mapping = {
        "16B": "deepseek-ai/DeepSeek-V3",  # We'll use the main repo but with 16B config
        "236B": "deepseek-ai/DeepSeek-V3",
        "671B": "deepseek-ai/DeepSeek-V3"
    }
    
    if model_size not in config_mapping:
        raise ValueError(f"Unsupported model size: {model_size}")
    
    repo_id = config_mapping[model_size]
    
    # Download just the config files first
    files_to_download = [
        "config.json",
        "configuration_deepseek.py",
        f"inference/configs/config_{model_size}.json"
    ]
    
    model_dir = Path(f"models/DeepSeek-V3-{model_size}")
    model_dir.mkdir(parents=True, exist_ok=True)
    
    for file_path in files_to_download:
        try:
            local_path = hf_hub_download(
                repo_id=repo_id,
                filename=file_path,
                local_dir=model_dir,
                local_dir_use_symlinks=False
            )
            print(f"✅ Downloaded: {file_path}")
        except Exception as e:
            print(f"⚠️  Could not download {file_path}: {e}")
    
    return model_dir

def create_dev_scripts():
    """Create helpful development scripts"""
    print("📝 Creating development scripts...")
    
    # Create a simple test script
    test_script = """#!/usr/bin/env python3
import sys
import os
sys.path.append('inference')

from model import Transformer, ModelArgs
import torch
import json

def test_model_loading():
    print("Testing model loading...")
    
    # Load config
    with open('inference/configs/config_16B.json', 'r') as f:
        config = json.load(f)
    
    args = ModelArgs(**config)
    print(f"Model args: {args}")
    
    # Create model (without loading weights)
    model = Transformer(args)
    print(f"Model created successfully!")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    return True

if __name__ == "__main__":
    test_model_loading()
"""
    
    with open("test_model.py", "w") as f:
        f.write(test_script)
    
    # Create a Jupyter notebook for development
    notebook_content = {
        "cells": [
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": ["# DeepSeek-V3 Development Notebook\n", "This notebook helps you get started with DeepSeek-V3 development."]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "import sys\n",
                    "sys.path.append('inference')\n",
                    "\n",
                    "import torch\n",
                    "import json\n",
                    "from model import Transformer, ModelArgs\n",
                    "from transformers import AutoTokenizer\n",
                    "\n",
                    "print(f\"PyTorch version: {torch.__version__}\")\n",
                    "print(f\"CUDA available: {torch.cuda.is_available()}\")"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# Load model configuration\n",
                    "with open('inference/configs/config_16B.json', 'r') as f:\n",
                    "    config = json.load(f)\n",
                    "\n",
                    "print(\"Model configuration:\")\n",
                    "for key, value in config.items():\n",
                    "    print(f\"  {key}: {value}\")"
                ]
            }
        ],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "name": "python",
                "version": "3.13.5"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    
    with open("deepseek_v3_dev.ipynb", "w") as f:
        json.dump(notebook_content, f, indent=2)
    
    print("✅ Created test_model.py")
    print("✅ Created deepseek_v3_dev.ipynb")

def main():
    parser = argparse.ArgumentParser(description="DeepSeek-V3 Development Setup")
    parser.add_argument("--model-size", choices=["16B", "236B", "671B"], default="16B",
                       help="Model size to download (default: 16B for development)")
    parser.add_argument("--skip-download", action="store_true",
                       help="Skip model download")
    
    args = parser.parse_args()
    
    print("🚀 Setting up DeepSeek-V3 Development Environment")
    print("=" * 50)
    
    # Check system requirements
    check_system_requirements()
    print()
    
    # Download model config
    if not args.skip_download:
        model_dir = download_model_config(args.model_size)
        print(f"✅ Model configuration downloaded to: {model_dir}")
    print()
    
    # Create development scripts
    create_dev_scripts()
    print()
    
    print("🎉 Development environment setup complete!")
    print("\nNext steps:")
    print("1. Run: python test_model.py")
    print("2. Start Jupyter: jupyter lab deepseek_v3_dev.ipynb")
    print("3. For full model weights, run with --model-size 671B")

if __name__ == "__main__":
    main()
