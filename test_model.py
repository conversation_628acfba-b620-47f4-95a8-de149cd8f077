#!/usr/bin/env python3
import sys
import os
import torch
import json

def test_config_loading():
    """Test loading and parsing model configuration"""
    print("🔍 Testing configuration loading...")

    # Load config
    config_path = 'inference/configs/config_16B.json'
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        return False

    with open(config_path, 'r') as f:
        config = json.load(f)

    print("✅ Configuration loaded successfully!")
    print("📋 Model configuration:")
    for key, value in config.items():
        print(f"   {key}: {value}")

    return True

def test_basic_torch():
    """Test basic PyTorch functionality"""
    print("\n🔍 Testing PyTorch functionality...")

    # Test tensor creation
    x = torch.randn(2, 3)
    print(f"✅ Created tensor: {x.shape}")

    # Test basic operations
    y = torch.matmul(x, x.T)
    print(f"✅ Matrix multiplication works: {y.shape}")

    # Check device
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"✅ Using device: {device}")

    return True

def test_model_structure():
    """Test model structure without Triton dependencies"""
    print("\n🔍 Testing model structure (without weights)...")

    try:
        # Try to import without Triton-dependent parts
        sys.path.append('inference')

        # Load config
        with open('inference/configs/config_16B.json', 'r') as f:
            config = json.load(f)

        print("✅ Model configuration loaded")

        # Calculate expected parameter count
        vocab_size = config['vocab_size']
        dim = config['dim']
        n_layers = config['n_layers']

        # Rough parameter estimation
        embedding_params = vocab_size * dim
        layer_params = n_layers * (dim * dim * 4)  # Simplified estimation
        total_params = embedding_params + layer_params

        print(f"📊 Estimated parameters: ~{total_params:,}")
        print(f"📊 Expected size: ~{total_params * 2 / 1e9:.1f}B parameters")

        return True

    except ImportError as e:
        print(f"⚠️  Import error (expected on macOS): {e}")
        print("✅ This is normal - Triton is not available on macOS")
        return True
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    print("🚀 DeepSeek-V3 Development Environment Test")
    print("=" * 50)

    success = True

    # Test configuration loading
    success &= test_config_loading()

    # Test PyTorch
    success &= test_basic_torch()

    # Test model structure
    success &= test_model_structure()

    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! Development environment is ready.")
        print("\n📝 Next steps:")
        print("1. Start Jupyter Lab: jupyter lab deepseek_v3_dev.ipynb")
        print("2. For full inference, you'll need actual model weights")
        print("3. Consider using SGLang or other frameworks for production inference")
    else:
        print("❌ Some tests failed. Check the output above.")

    return success

if __name__ == "__main__":
    main()
