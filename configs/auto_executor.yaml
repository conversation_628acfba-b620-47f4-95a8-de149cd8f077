# Automatische Taakuitvoering Configuratie
# DeepSeek-V3 Project

triggers:
  file_changes:
    enabled: true
    patterns:
      - "*.py"
      - "*.md" 
      - "*.json"
      - "*.yaml"
      - "*.sh"
    actions:
      - "lint"
      - "test_quick"
    exclude_paths:
      - "deepseek_env/"
      - ".git/"
      - "__pycache__/"
      - "*.pyc"

  git_events:
    enabled: true
    on_commit:
      - "test_quick"
      - "lint"
    on_push:
      - "test_full"
      - "security_check"
    on_merge:
      - "test_full"
      - "update_docs"

  schedule:
    enabled: true
    daily_tasks:
      - "cleanup"
      - "update_docs"
      - "backup_config"
    weekly_tasks:
      - "test_full"
      - "security_scan"
      - "performance_check"
    monthly_tasks:
      - "dependency_update"
      - "model_validation"

tasks:
  # Code Quality Tasks
  lint:
    command: "flake8 . --count --statistics --exclude=deepseek_env"
    timeout: 60
    retry_count: 1
    description: "Run code linting"

  format:
    command: "black . --exclude=deepseek_env"
    timeout: 120
    retry_count: 1
    description: "Format code with black"

  type_check:
    command: "mypy . --ignore-missing-imports"
    timeout: 180
    retry_count: 1
    description: "Run type checking"

  # Testing Tasks
  test_quick:
    command: "python -m pytest tests/ -x --tb=short"
    timeout: 300
    retry_count: 2
    description: "Run quick tests"

  test_full:
    command: "./scripts/run_tests.sh"
    timeout: 600
    retry_count: 1
    description: "Run full test suite"
    requires:
      - "lint"

  test_integration:
    command: "python -m pytest tests/integration/ -v"
    timeout: 900
    retry_count: 1
    description: "Run integration tests"

  # Security Tasks
  security_check:
    command: "bandit -r . -x deepseek_env"
    timeout: 120
    retry_count: 1
    description: "Run security checks"

  security_scan:
    command: "safety check"
    timeout: 60
    retry_count: 1
    description: "Scan for security vulnerabilities"

  # Documentation Tasks
  update_docs:
    command: "./scripts/update_docs.sh"
    timeout: 180
    retry_count: 1
    description: "Update documentation"

  generate_api_docs:
    command: "sphinx-build -b html docs/ docs/_build/"
    timeout: 300
    retry_count: 1
    description: "Generate API documentation"

  # Deployment Tasks
  deploy_staging:
    command: "./scripts/deploy.sh staging"
    timeout: 600
    retry_count: 1
    description: "Deploy to staging environment"
    requires:
      - "test_full"
      - "security_check"

  deploy_production:
    command: "./scripts/deploy.sh production"
    timeout: 900
    retry_count: 0
    description: "Deploy to production environment"
    requires:
      - "test_full"
      - "security_scan"
      - "performance_check"

  # Maintenance Tasks
  cleanup:
    command: |
      find . -name "*.pyc" -delete
      find . -name "__pycache__" -type d -exec rm -rf {} +
      find . -name ".pytest_cache" -type d -exec rm -rf {} +
    timeout: 60
    retry_count: 1
    description: "Clean up temporary files"

  backup_config:
    command: "tar -czf backups/config_$(date +%Y%m%d).tar.gz configs/ .vscode/"
    timeout: 120
    retry_count: 1
    description: "Backup configuration files"

  dependency_update:
    command: "pip list --outdated --format=json | jq -r '.[] | .name' | xargs -I {} pip install --upgrade {}"
    timeout: 600
    retry_count: 1
    description: "Update Python dependencies"

  # Model Tasks
  model_validation:
    command: "python scripts/validate_models.py"
    timeout: 1800
    retry_count: 1
    description: "Validate model integrity"

  performance_check:
    command: "python scripts/performance_benchmark.py"
    timeout: 3600
    retry_count: 1
    description: "Run performance benchmarks"

  # Environment Tasks
  env_check:
    command: "python scripts/check_environment.py"
    timeout: 60
    retry_count: 1
    description: "Check environment health"

  setup_env:
    command: "./scripts/setup_env.sh"
    timeout: 600
    retry_count: 1
    description: "Setup development environment"

# Notification settings
notifications:
  enabled: true
  channels:
    - "console"
    - "file"
  file_path: "logs/auto_executor.log"
  
  # Notify on these events
  on_task_failure: true
  on_task_success: false
  on_critical_failure: true

# Resource limits
limits:
  max_concurrent_tasks: 3
  max_queue_size: 50
  max_execution_time: 3600
  memory_limit: "2GB"

# Retry policies
retry:
  default_count: 1
  backoff_factor: 2
  max_backoff: 300
