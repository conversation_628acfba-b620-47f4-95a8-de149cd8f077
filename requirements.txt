# DeepSeek-V3 Project Dependencies

# Core ML/AI Libraries
torch>=2.4.1
transformers>=4.46.3
safetensors>=0.4.5
triton>=3.0.0

# Development Tools
pytest>=7.0.0
pytest-cov>=4.0.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0
isort>=5.12.0

# Code Quality & Security
bandit>=1.7.0
safety>=2.3.0
pre-commit>=3.0.0

# Documentation
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0
myst-parser>=1.0.0

# Monitoring & Logging
watchdog>=3.0.0
pyyaml>=6.0
structlog>=23.0.0

# API & Web
fastapi>=0.100.0
uvicorn>=0.23.0
requests>=2.31.0

# Data Processing
numpy>=1.24.0
pandas>=2.0.0
pillow>=10.0.0

# Testing & Benchmarking
memory-profiler>=0.61.0
psutil>=5.9.0
tqdm>=4.65.0

# Utilities
click>=8.1.0
rich>=13.0.0
typer>=0.9.0
jinja2>=3.1.0

# Development Environment
jupyter>=1.0.0
ipython>=8.0.0
notebook>=6.5.0

# Version Control & CI/CD
gitpython>=3.1.0

# Optional: GPU Monitoring (uncomment if needed)
# nvidia-ml-py>=12.0.0
# gpustat>=1.1.0
