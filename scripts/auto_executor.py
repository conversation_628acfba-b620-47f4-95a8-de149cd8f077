#!/usr/bin/env python3
"""
Automatische Taakuitvoering voor DeepSeek-V3 Project
Monitort en voert taken automatisch uit volgens de regels in main.md
"""

import json
import logging
import os
import subprocess
import time
import yaml
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('auto-executor')

class TaskExecutor:
    """Automatische taakuitvoering engine"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.config_file = self.project_root / "configs" / "auto_executor.yaml"
        self.task_queue = []
        self.running_tasks = {}
        self.load_config()
        
    def load_config(self):
        """Laad configuratie voor automatische uitvoering"""
        default_config = {
            'triggers': {
                'file_changes': {
                    'enabled': True,
                    'patterns': ['*.py', '*.md', '*.json', '*.yaml'],
                    'actions': ['lint', 'test']
                },
                'git_events': {
                    'enabled': True,
                    'on_commit': ['test'],
                    'on_push': ['test', 'deploy_staging']
                },
                'schedule': {
                    'enabled': True,
                    'daily_tasks': ['update_docs', 'cleanup'],
                    'weekly_tasks': ['full_test_suite', 'security_scan']
                }
            },
            'tasks': {
                'lint': {
                    'command': 'flake8 . --count --statistics',
                    'timeout': 60,
                    'retry_count': 1
                },
                'test': {
                    'command': './scripts/run_tests.sh',
                    'timeout': 300,
                    'retry_count': 2
                },
                'deploy_staging': {
                    'command': './scripts/deploy.sh staging',
                    'timeout': 600,
                    'retry_count': 1,
                    'requires': ['test']
                },
                'update_docs': {
                    'command': './scripts/update_docs.sh',
                    'timeout': 120,
                    'retry_count': 1
                },
                'cleanup': {
                    'command': 'find . -name "*.pyc" -delete',
                    'timeout': 30,
                    'retry_count': 1
                }
            }
        }
        
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    self.config = yaml.safe_load(f)
            except Exception as e:
                logger.error(f"Error loading config: {e}")
                self.config = default_config
        else:
            self.config = default_config
            self.save_config()
    
    def save_config(self):
        """Sla configuratie op"""
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        try:
            with open(self.config_file, 'w') as f:
                yaml.dump(self.config, f, default_flow_style=False)
        except Exception as e:
            logger.error(f"Error saving config: {e}")
    
    def add_task(self, task_name: str, priority: int = 5, context: Dict[str, Any] = None):
        """Voeg taak toe aan queue"""
        task = {
            'name': task_name,
            'priority': priority,
            'context': context or {},
            'created': datetime.now().isoformat(),
            'attempts': 0
        }
        
        # Voeg toe op basis van prioriteit
        inserted = False
        for i, existing_task in enumerate(self.task_queue):
            if priority < existing_task['priority']:
                self.task_queue.insert(i, task)
                inserted = True
                break
        
        if not inserted:
            self.task_queue.append(task)
        
        logger.info(f"Task added to queue: {task_name} (priority: {priority})")
    
    def execute_task(self, task_name: str, context: Dict[str, Any] = None) -> bool:
        """Voer een specifieke taak uit"""
        if task_name not in self.config['tasks']:
            logger.error(f"Unknown task: {task_name}")
            return False
        
        task_config = self.config['tasks'][task_name]
        
        # Check dependencies
        if 'requires' in task_config:
            for required_task in task_config['requires']:
                if not self.execute_task(required_task, context):
                    logger.error(f"Required task failed: {required_task}")
                    return False
        
        # Execute task
        try:
            logger.info(f"Executing task: {task_name}")
            
            result = subprocess.run(
                task_config['command'],
                shell=True,
                capture_output=True,
                text=True,
                timeout=task_config.get('timeout', 300),
                cwd=self.project_root
            )
            
            if result.returncode == 0:
                logger.info(f"Task completed successfully: {task_name}")
                return True
            else:
                logger.error(f"Task failed: {task_name}")
                logger.error(f"Error output: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"Task timed out: {task_name}")
            return False
        except Exception as e:
            logger.error(f"Error executing task {task_name}: {e}")
            return False
    
    def process_queue(self):
        """Verwerk task queue"""
        while self.task_queue:
            task = self.task_queue.pop(0)
            task_name = task['name']
            
            # Check retry limit
            max_attempts = self.config['tasks'].get(task_name, {}).get('retry_count', 1) + 1
            if task['attempts'] >= max_attempts:
                logger.error(f"Task exceeded max attempts: {task_name}")
                continue
            
            task['attempts'] += 1
            
            # Execute task
            success = self.execute_task(task_name, task['context'])
            
            if not success and task['attempts'] < max_attempts:
                # Re-queue for retry
                task['priority'] += 1  # Lower priority for retry
                self.task_queue.append(task)
                logger.info(f"Task re-queued for retry: {task_name}")
    
    def start_monitoring(self):
        """Start file system monitoring"""
        if not self.config['triggers']['file_changes']['enabled']:
            return
        
        class ChangeHandler(FileSystemEventHandler):
            def __init__(self, executor):
                self.executor = executor
            
            def on_modified(self, event):
                if event.is_directory:
                    return
                
                file_path = Path(event.src_path)
                patterns = self.executor.config['triggers']['file_changes']['patterns']
                
                # Check if file matches patterns
                for pattern in patterns:
                    if file_path.match(pattern):
                        actions = self.executor.config['triggers']['file_changes']['actions']
                        for action in actions:
                            self.executor.add_task(action, priority=3)
                        break
        
        observer = Observer()
        observer.schedule(ChangeHandler(self), str(self.project_root), recursive=True)
        observer.start()
        logger.info("File system monitoring started")
        
        return observer
    
    def run(self):
        """Main execution loop"""
        logger.info("Auto Executor started")
        
        # Start file monitoring
        observer = self.start_monitoring()
        
        try:
            while True:
                # Process task queue
                self.process_queue()
                
                # Sleep before next iteration
                time.sleep(5)
                
        except KeyboardInterrupt:
            logger.info("Auto Executor stopped")
        finally:
            if observer:
                observer.stop()
                observer.join()

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='DeepSeek-V3 Auto Executor')
    parser.add_argument('--project-root', default='.', help='Project root directory')
    parser.add_argument('--task', help='Execute specific task')
    parser.add_argument('--daemon', action='store_true', help='Run as daemon')
    
    args = parser.parse_args()
    
    executor = TaskExecutor(args.project_root)
    
    if args.task:
        # Execute single task
        success = executor.execute_task(args.task)
        exit(0 if success else 1)
    elif args.daemon:
        # Run as daemon
        executor.run()
    else:
        # Process current queue and exit
        executor.process_queue()

if __name__ == '__main__':
    main()
