#!/bin/bash
# DeepSeek-V3 Environment Setup Script

set -e

echo "🚀 Setting up DeepSeek-V3 development environment..."

# Check if Python virtual environment exists
if [ ! -d "deepseek_env" ]; then
    echo "📦 Creating Python virtual environment..."
    python3 -m venv deepseek_env
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source deepseek_env/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install requirements
if [ -f "requirements.txt" ]; then
    echo "📋 Installing Python dependencies..."
    pip install -r requirements.txt
fi

if [ -f "inference/requirements.txt" ]; then
    echo "📋 Installing inference dependencies..."
    pip install -r inference/requirements.txt
fi

# Create necessary directories
echo "📁 Creating project directories..."
mkdir -p models
mkdir -p tests
mkdir -p docs
mkdir -p configs
mkdir -p logs

# Set up git hooks if .git exists
if [ -d ".git" ]; then
    echo "🔗 Setting up git hooks..."
    mkdir -p .git/hooks
    
    # Pre-commit hook
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
echo "Running pre-commit checks..."
python -m flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
python -m pytest tests/ -x
EOF
    chmod +x .git/hooks/pre-commit
fi

# Create initial config files
echo "⚙️ Creating configuration files..."

# Create pytest config
cat > pytest.ini << 'EOF'
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short
EOF

# Create flake8 config
cat > .flake8 << 'EOF'
[flake8]
max-line-length = 88
extend-ignore = E203, W503
exclude = 
    .git,
    __pycache__,
    deepseek_env,
    .venv,
    build,
    dist
EOF

# Make scripts executable
echo "🔐 Setting script permissions..."
chmod +x scripts/*.py
chmod +x scripts/*.sh

echo "✅ Environment setup complete!"
echo ""
echo "To activate the environment, run:"
echo "source deepseek_env/bin/activate"
echo ""
echo "To start development, run:"
echo "./start_dev.sh"
