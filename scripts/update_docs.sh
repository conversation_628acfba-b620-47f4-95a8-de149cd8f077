#!/bin/bash
# Documentation Update Script voor DeepSeek-V3

set -e

echo "📚 Updating DeepSeek-V3 documentation..."

# Create docs directory if it doesn't exist
mkdir -p docs

# Activate virtual environment if it exists
if [ -d "deepseek_env" ]; then
    echo "🔧 Activating virtual environment..."
    source deepseek_env/bin/activate
fi

# Generate API documentation from docstrings
echo "📖 Generating API documentation..."
if command -v sphinx-apidoc &> /dev/null; then
    sphinx-apidoc -o docs/ . --separate --force
    echo "✅ API documentation generated"
else
    echo "⚠️ Sphinx not installed, skipping API docs generation"
fi

# Update README with current project status
echo "📝 Updating README..."
python3 << 'EOF'
import json
import os
from datetime import datetime
from pathlib import Path

# Read current README
readme_path = Path("README.md")
if readme_path.exists():
    with open(readme_path, 'r') as f:
        content = f.read()
    
    # Add last updated timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Look for existing timestamp line and replace it
    lines = content.split('\n')
    updated = False
    
    for i, line in enumerate(lines):
        if line.startswith('**Last Updated:**'):
            lines[i] = f'**Last Updated:** {timestamp}'
            updated = True
            break
    
    if not updated:
        # Add timestamp at the end
        lines.append('')
        lines.append(f'**Last Updated:** {timestamp}')
    
    # Write back
    with open(readme_path, 'w') as f:
        f.write('\n'.join(lines))
    
    print("✅ README updated with timestamp")
else:
    print("⚠️ README.md not found")
EOF

# Generate project statistics
echo "📊 Generating project statistics..."
python3 << 'EOF'
import os
import json
from pathlib import Path
from datetime import datetime

stats = {
    'generated_at': datetime.now().isoformat(),
    'python_files': 0,
    'total_lines': 0,
    'test_files': 0,
    'config_files': 0,
    'documentation_files': 0
}

# Count files and lines
for file_path in Path('.').rglob('*'):
    if file_path.is_file() and not any(part.startswith('.') for part in file_path.parts):
        if file_path.suffix == '.py':
            stats['python_files'] += 1
            if 'test' in file_path.name.lower():
                stats['test_files'] += 1
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    stats['total_lines'] += len(f.readlines())
            except:
                pass
        elif file_path.suffix in ['.json', '.yaml', '.yml']:
            stats['config_files'] += 1
        elif file_path.suffix in ['.md', '.rst', '.txt']:
            stats['documentation_files'] += 1

# Save statistics
with open('docs/project_stats.json', 'w') as f:
    json.dump(stats, f, indent=2)

print(f"✅ Project statistics generated:")
print(f"  Python files: {stats['python_files']}")
print(f"  Total lines: {stats['total_lines']}")
print(f"  Test files: {stats['test_files']}")
print(f"  Config files: {stats['config_files']}")
print(f"  Documentation files: {stats['documentation_files']}")
EOF

# Generate changelog from git commits
echo "📋 Generating changelog..."
if [ -d ".git" ]; then
    echo "# Changelog" > docs/CHANGELOG.md
    echo "" >> docs/CHANGELOG.md
    echo "Generated on $(date)" >> docs/CHANGELOG.md
    echo "" >> docs/CHANGELOG.md
    
    # Get recent commits
    git log --oneline --max-count=20 --pretty=format:"- %s (%h)" >> docs/CHANGELOG.md
    echo "✅ Changelog generated"
else
    echo "⚠️ Not a git repository, skipping changelog"
fi

# Create documentation index
echo "📑 Creating documentation index..."
cat > docs/README.md << 'EOF'
# DeepSeek-V3 Documentation

## Project Overview
This directory contains all documentation for the DeepSeek-V3 project.

## Contents

### Core Documentation
- [Main Rules](../main.md) - Project rules and guidelines
- [Project README](../README.md) - Main project documentation
- [Changelog](CHANGELOG.md) - Recent changes and updates

### Technical Documentation
- [API Documentation](api/) - Generated API documentation
- [Configuration Guide](../configs/) - Configuration files and examples
- [Deployment Guide](deployment.md) - Deployment instructions

### Development
- [Development Setup](../scripts/setup_env.sh) - Environment setup
- [Testing Guide](testing.md) - Testing procedures
- [Contributing](contributing.md) - Contribution guidelines

### Statistics
- [Project Statistics](project_stats.json) - Current project metrics

## Quick Links
- [GitHub Repository](https://github.com/deepseek-ai/DeepSeek-V3)
- [Hugging Face Model](https://huggingface.co/deepseek-ai/DeepSeek-V3)
- [Paper](https://arxiv.org/pdf/2412.19437)

---
*Documentation last updated: $(date)*
EOF

echo "✅ Documentation index created"

# Validate documentation links
echo "🔗 Validating documentation links..."
python3 << 'EOF'
import re
from pathlib import Path

def check_links_in_file(file_path):
    """Check for broken internal links in markdown files"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find markdown links
        links = re.findall(r'\[([^\]]+)\]\(([^)]+)\)', content)
        broken_links = []
        
        for link_text, link_url in links:
            # Skip external links
            if link_url.startswith(('http://', 'https://', 'mailto:')):
                continue
            
            # Check if file exists
            link_path = Path(file_path).parent / link_url
            if not link_path.exists():
                broken_links.append((link_text, link_url))
        
        return broken_links
    except Exception as e:
        print(f"Error checking {file_path}: {e}")
        return []

# Check all markdown files
total_broken = 0
for md_file in Path('.').rglob('*.md'):
    if not any(part.startswith('.') for part in md_file.parts):
        broken = check_links_in_file(md_file)
        if broken:
            print(f"⚠️ Broken links in {md_file}:")
            for text, url in broken:
                print(f"  - [{text}]({url})")
            total_broken += len(broken)

if total_broken == 0:
    print("✅ No broken links found")
else:
    print(f"⚠️ Found {total_broken} broken links")
EOF

echo "🎉 Documentation update complete!"
echo ""
echo "Generated documentation:"
echo "  - docs/README.md - Documentation index"
echo "  - docs/CHANGELOG.md - Recent changes"
echo "  - docs/project_stats.json - Project statistics"
