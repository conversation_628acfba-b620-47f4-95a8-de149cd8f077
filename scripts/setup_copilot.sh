#!/bin/bash
# GitHub Copilot Setup voor DeepSeek-V3 Project

set -e

echo "🤖 Setting up GitHub Copilot for DeepSeek-V3..."

# Check if VS Code is installed
if ! command -v code &> /dev/null; then
    echo "❌ VS Code not found. Please install VS Code first."
    exit 1
fi

# Check if Copilot extensions are installed
echo "🔍 Checking Copilot extensions..."
if ! code --list-extensions | grep -q "github.copilot"; then
    echo "📦 Installing GitHub Copilot extension..."
    code --install-extension GitHub.copilot
else
    echo "✅ GitHub Copilot extension already installed"
fi

if ! code --list-extensions | grep -q "github.copilot-chat"; then
    echo "📦 Installing GitHub Copilot Chat extension..."
    code --install-extension GitHub.copilot-chat
else
    echo "✅ GitHub Copilot Chat extension already installed"
fi

# Check authentication status
echo "🔐 Checking Copilot authentication..."
echo "Please make sure you're signed in to GitHub in VS Code."
echo "You can check this by:"
echo "1. Opening VS Code"
echo "2. Opening Command Palette (Cmd+Shift+P)"
echo "3. Running 'GitHub Copilot: Sign In'"

# Create Copilot workspace settings
echo "⚙️ Creating Copilot workspace settings..."
mkdir -p .vscode

# Test Copilot functionality
echo "🧪 Testing Copilot functionality..."
cat > test_copilot.py << 'EOF'
# Test file for GitHub Copilot
# Type a comment like "# Function to calculate fibonacci numbers" 
# and see if Copilot suggests code

def main():
    # TODO: Test Copilot suggestions here
    pass

if __name__ == "__main__":
    main()
EOF

echo "✅ Copilot setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Open VS Code: code ."
echo "2. Sign in to GitHub if not already done"
echo "3. Open test_copilot.py and try typing comments"
echo "4. Use Ctrl+Space to trigger suggestions"
echo "5. Use Cmd+Shift+P and search for 'Copilot' commands"
echo ""
echo "🔧 Useful Copilot shortcuts:"
echo "  - Tab: Accept suggestion"
echo "  - Esc: Dismiss suggestion"
echo "  - Alt+]: Next suggestion"
echo "  - Alt+[: Previous suggestion"
echo "  - Cmd+Shift+P > 'Copilot: Open Chat': Open Copilot Chat"
echo ""
echo "💡 For DeepSeek-V3 development:"
echo "  - Use descriptive comments to get better suggestions"
echo "  - Include type hints for better context"
echo "  - Use docstrings to describe function behavior"

# Clean up test file
rm -f test_copilot.py
