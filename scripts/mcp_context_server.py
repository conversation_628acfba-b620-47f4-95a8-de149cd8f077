#!/usr/bin/env python3
"""
MCP Context Server voor DeepSeek-V3 Project
Beheert context en state voor AI-integraties
"""

import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

# Setup logging
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('mcp-context-server')

class MCPContextServer:
    """MCP Context Server voor project context management"""
    
    def __init__(self):
        self.project_root = Path(os.getenv('PROJECT_ROOT', '.'))
        self.model_path = Path(os.getenv('MODEL_PATH', './models'))
        self.context_file = self.project_root / '.mcp_context.json'
        self.context_data = self.load_context()
        
    def load_context(self) -> Dict[str, Any]:
        """Laad bestaande context data"""
        if self.context_file.exists():
            try:
                with open(self.context_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Fout bij laden context: {e}")
        
        return {
            'project_info': {
                'name': 'DeepSeek-V3',
                'version': '1.0.0',
                'created': datetime.now().isoformat()
            },
            'active_models': [],
            'recent_tasks': [],
            'configurations': {},
            'session_history': []
        }
    
    def save_context(self):
        """Sla context data op"""
        try:
            with open(self.context_file, 'w', encoding='utf-8') as f:
                json.dump(self.context_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Fout bij opslaan context: {e}")
    
    def update_project_status(self, status: str, details: Dict[str, Any] = None):
        """Update project status"""
        self.context_data['project_info']['last_update'] = datetime.now().isoformat()
        self.context_data['project_info']['status'] = status
        if details:
            self.context_data['project_info']['details'] = details
        self.save_context()
        logger.info(f"Project status updated: {status}")
    
    def add_task_record(self, task_name: str, status: str, details: str = ""):
        """Voeg taak record toe"""
        task_record = {
            'name': task_name,
            'status': status,
            'timestamp': datetime.now().isoformat(),
            'details': details
        }
        
        self.context_data['recent_tasks'].append(task_record)
        
        # Houd alleen laatste 50 taken
        if len(self.context_data['recent_tasks']) > 50:
            self.context_data['recent_tasks'] = self.context_data['recent_tasks'][-50:]
        
        self.save_context()
        logger.info(f"Task record toegevoegd: {task_name} - {status}")
    
    def register_model(self, model_name: str, model_path: str, config: Dict[str, Any] = None):
        """Registreer een model"""
        model_info = {
            'name': model_name,
            'path': model_path,
            'registered': datetime.now().isoformat(),
            'config': config or {}
        }
        
        # Verwijder bestaande registratie van hetzelfde model
        self.context_data['active_models'] = [
            m for m in self.context_data['active_models'] 
            if m['name'] != model_name
        ]
        
        self.context_data['active_models'].append(model_info)
        self.save_context()
        logger.info(f"Model geregistreerd: {model_name}")
    
    def get_project_context(self) -> Dict[str, Any]:
        """Krijg volledige project context"""
        return {
            'project_root': str(self.project_root),
            'model_path': str(self.model_path),
            'context_data': self.context_data,
            'available_models': self.scan_available_models(),
            'recent_files': self.get_recent_files()
        }
    
    def scan_available_models(self) -> List[Dict[str, Any]]:
        """Scan beschikbare modellen"""
        models = []
        if self.model_path.exists():
            for model_dir in self.model_path.iterdir():
                if model_dir.is_dir():
                    models.append({
                        'name': model_dir.name,
                        'path': str(model_dir),
                        'size': self.get_dir_size(model_dir),
                        'modified': datetime.fromtimestamp(
                            model_dir.stat().st_mtime
                        ).isoformat()
                    })
        return models
    
    def get_dir_size(self, path: Path) -> int:
        """Krijg directory grootte"""
        total = 0
        try:
            for file_path in path.rglob('*'):
                if file_path.is_file():
                    total += file_path.stat().st_size
        except Exception:
            pass
        return total
    
    def get_recent_files(self) -> List[Dict[str, Any]]:
        """Krijg recent gewijzigde bestanden"""
        recent_files = []
        try:
            for file_path in self.project_root.rglob('*.py'):
                if file_path.is_file() and not any(
                    part.startswith('.') for part in file_path.parts
                ):
                    recent_files.append({
                        'path': str(file_path.relative_to(self.project_root)),
                        'modified': datetime.fromtimestamp(
                            file_path.stat().st_mtime
                        ).isoformat(),
                        'size': file_path.stat().st_size
                    })
            
            # Sorteer op modificatie tijd
            recent_files.sort(key=lambda x: x['modified'], reverse=True)
            return recent_files[:20]  # Laatste 20 bestanden
        except Exception as e:
            logger.error(f"Fout bij scannen bestanden: {e}")
            return []
    
    def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle MCP request"""
        method = request.get('method', '')
        params = request.get('params', {})
        
        try:
            if method == 'get_context':
                return {'result': self.get_project_context()}
            elif method == 'update_status':
                self.update_project_status(
                    params.get('status', ''),
                    params.get('details', {})
                )
                return {'result': 'success'}
            elif method == 'add_task':
                self.add_task_record(
                    params.get('name', ''),
                    params.get('status', ''),
                    params.get('details', '')
                )
                return {'result': 'success'}
            elif method == 'register_model':
                self.register_model(
                    params.get('name', ''),
                    params.get('path', ''),
                    params.get('config', {})
                )
                return {'result': 'success'}
            else:
                return {'error': f'Unknown method: {method}'}
        except Exception as e:
            logger.error(f"Fout bij verwerken request: {e}")
            return {'error': str(e)}

def main():
    """Main entry point"""
    server = MCPContextServer()
    logger.info("MCP Context Server gestart")
    
    # Initiële status update
    server.update_project_status('running', {
        'server': 'mcp-context-server',
        'pid': os.getpid()
    })
    
    # Eenvoudige request loop (in echte implementatie zou dit WebSocket/HTTP zijn)
    try:
        while True:
            # Placeholder voor echte MCP protocol implementatie
            import time
            time.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Server gestopt")
        server.update_project_status('stopped')

if __name__ == '__main__':
    main()
