#!/usr/bin/env python3
"""
MCP Task Server voor automatische taakuitvoering
"""

import json
import logging
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('mcp-task-server')

class MCPTaskServer:
    def __init__(self):
        self.project_root = Path(os.getenv('PROJECT_ROOT', '.'))
        self.task_db_path = Path(os.getenv('TASK_DB_PATH', '.tasks.json'))
        self.auto_execute = os.getenv('AUTO_EXECUTE', 'false').lower() == 'true'
        self.tasks = self.load_tasks()
        
    def load_tasks(self) -> List[Dict[str, Any]]:
        if self.task_db_path.exists():
            try:
                with open(self.task_db_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading tasks: {e}")
        return []
    
    def save_tasks(self):
        try:
            with open(self.task_db_path, 'w') as f:
                json.dump(self.tasks, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving tasks: {e}")
    
    def add_task(self, name: str, command: str, auto_run: bool = False):
        task = {
            'id': len(self.tasks) + 1,
            'name': name,
            'command': command,
            'auto_run': auto_run,
            'status': 'pending',
            'created': datetime.now().isoformat(),
            'last_run': None,
            'output': None
        }
        self.tasks.append(task)
        self.save_tasks()
        logger.info(f"Task added: {name}")
        
        if auto_run and self.auto_execute:
            self.execute_task(task['id'])
    
    def execute_task(self, task_id: int):
        task = next((t for t in self.tasks if t['id'] == task_id), None)
        if not task:
            logger.error(f"Task {task_id} not found")
            return
        
        try:
            logger.info(f"Executing task: {task['name']}")
            result = subprocess.run(
                task['command'],
                shell=True,
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            task['status'] = 'completed' if result.returncode == 0 else 'failed'
            task['last_run'] = datetime.now().isoformat()
            task['output'] = {
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            self.save_tasks()
            logger.info(f"Task {task['name']} {task['status']}")
            
        except Exception as e:
            task['status'] = 'error'
            task['output'] = {'error': str(e)}
            self.save_tasks()
            logger.error(f"Error executing task {task['name']}: {e}")

def main():
    server = MCPTaskServer()
    
    # Voeg standaard taken toe
    server.add_task("Setup Environment", "python dev_setup.py", True)
    server.add_task("Run Tests", "python -m pytest tests/", False)
    server.add_task("Update Dependencies", "pip install -r requirements.txt", False)
    
    logger.info("MCP Task Server started")

if __name__ == '__main__':
    main()
