#!/bin/bash
# DeepSeek-V3 Deployment Script

set -e

echo "🚀 Starting DeepSeek-V3 deployment..."

# Configuration
ENVIRONMENT=${1:-staging}
VERSION=$(git describe --tags --always)
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "📋 Deployment Configuration:"
echo "  Environment: $ENVIRONMENT"
echo "  Version: $VERSION"
echo "  Timestamp: $TIMESTAMP"

# Pre-deployment checks
echo "🔍 Running pre-deployment checks..."

# Check if tests pass
echo "🧪 Running tests..."
./scripts/run_tests.sh

# Check if all required files exist
echo "📁 Checking required files..."
required_files=(
    "main.md"
    "README.md"
    "inference/model.py"
    "inference/generate.py"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ Required file missing: $file"
        exit 1
    fi
done

echo "✅ All required files present"

# Check model files
echo "🤖 Checking model availability..."
if [ ! -d "models" ] || [ -z "$(ls -A models)" ]; then
    echo "⚠️ No models found in models/ directory"
    echo "Please download models before deployment"
    exit 1
fi

# Build deployment package
echo "📦 Building deployment package..."
DEPLOY_DIR="deploy_${ENVIRONMENT}_${TIMESTAMP}"
mkdir -p "$DEPLOY_DIR"

# Copy necessary files
cp -r inference/ "$DEPLOY_DIR/"
cp -r scripts/ "$DEPLOY_DIR/"
cp main.md README.md "$DEPLOY_DIR/"

# Copy configuration
if [ -f "configs/config_${ENVIRONMENT}.json" ]; then
    cp "configs/config_${ENVIRONMENT}.json" "$DEPLOY_DIR/config.json"
else
    echo "⚠️ No environment-specific config found, using default"
    cp "inference/configs/config_671B.json" "$DEPLOY_DIR/config.json"
fi

# Create deployment manifest
cat > "$DEPLOY_DIR/deployment_manifest.json" << EOF
{
    "environment": "$ENVIRONMENT",
    "version": "$VERSION",
    "timestamp": "$TIMESTAMP",
    "deployed_by": "$(whoami)",
    "git_commit": "$(git rev-parse HEAD)",
    "files_included": [
        $(find "$DEPLOY_DIR" -type f | sed 's/.*/"&"/' | paste -sd,)
    ]
}
EOF

# Create startup script
cat > "$DEPLOY_DIR/start_server.sh" << 'EOF'
#!/bin/bash
echo "🚀 Starting DeepSeek-V3 server..."

# Check if models are available
if [ ! -d "../models" ]; then
    echo "❌ Models directory not found"
    exit 1
fi

# Start the inference server
python generate.py \
    --ckpt-path ../models/DeepSeek-V3 \
    --config config.json \
    --port 8080 \
    --host 0.0.0.0
EOF

chmod +x "$DEPLOY_DIR/start_server.sh"

# Environment-specific deployment
case $ENVIRONMENT in
    "staging")
        echo "🔧 Deploying to staging environment..."
        # Add staging-specific deployment logic here
        ;;
    "production")
        echo "🏭 Deploying to production environment..."
        # Add production-specific deployment logic here
        # Additional safety checks for production
        echo "⚠️ Production deployment requires manual confirmation"
        read -p "Are you sure you want to deploy to production? (yes/no): " confirm
        if [ "$confirm" != "yes" ]; then
            echo "❌ Deployment cancelled"
            exit 1
        fi
        ;;
    "local")
        echo "💻 Setting up local deployment..."
        # Local deployment logic
        ;;
    *)
        echo "❌ Unknown environment: $ENVIRONMENT"
        echo "Supported environments: staging, production, local"
        exit 1
        ;;
esac

# Create deployment archive
echo "📦 Creating deployment archive..."
tar -czf "${DEPLOY_DIR}.tar.gz" "$DEPLOY_DIR"

echo "✅ Deployment package created: ${DEPLOY_DIR}.tar.gz"
echo "📁 Deployment directory: $DEPLOY_DIR"
echo ""
echo "🎉 Deployment preparation complete!"
echo ""
echo "Next steps:"
echo "1. Transfer ${DEPLOY_DIR}.tar.gz to target server"
echo "2. Extract and run start_server.sh"
echo "3. Monitor logs for successful startup"
