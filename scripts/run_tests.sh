#!/bin/bash
# DeepSeek-V3 Test Runner Script

set -e

echo "🧪 Running DeepSeek-V3 tests..."

# Activate virtual environment if it exists
if [ -d "deepseek_env" ]; then
    echo "🔧 Activating virtual environment..."
    source deepseek_env/bin/activate
fi

# Create tests directory if it doesn't exist
mkdir -p tests

# Run linting first
echo "🔍 Running code quality checks..."
if command -v flake8 &> /dev/null; then
    flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
    echo "✅ Linting passed"
else
    echo "⚠️ flake8 not installed, skipping linting"
fi

# Run type checking
if command -v mypy &> /dev/null; then
    echo "🔍 Running type checking..."
    mypy . --ignore-missing-imports
    echo "✅ Type checking passed"
else
    echo "⚠️ mypy not installed, skipping type checking"
fi

# Run unit tests
echo "🧪 Running unit tests..."
if command -v pytest &> /dev/null; then
    pytest tests/ -v --tb=short
    echo "✅ Unit tests passed"
else
    echo "⚠️ pytest not installed, running basic Python tests"
    python -m unittest discover tests/ -v
fi

# Run integration tests if they exist
if [ -d "tests/integration" ]; then
    echo "🔗 Running integration tests..."
    pytest tests/integration/ -v --tb=short
    echo "✅ Integration tests passed"
fi

# Run performance tests if they exist
if [ -d "tests/performance" ]; then
    echo "⚡ Running performance tests..."
    pytest tests/performance/ -v --tb=short
    echo "✅ Performance tests passed"
fi

# Generate coverage report
if command -v coverage &> /dev/null; then
    echo "📊 Generating coverage report..."
    coverage run -m pytest tests/
    coverage report -m
    coverage html
    echo "✅ Coverage report generated in htmlcov/"
fi

echo "🎉 All tests completed successfully!"
