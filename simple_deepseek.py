#!/usr/bin/env python3
"""
DeepSeek-V3 Simpele Interface voor macOS
Gemaakt door Leon's AI Assistant
"""
import os

import torch
from huggingface_hub import login
from transformers import AutoModelForCausalLM, AutoTokenizer


class DeepSeekV3:
    def __init__(self, model_name="deepseek-ai/DeepSeek-V3"):
        self.model_name = model_name
        self.tokenizer = None
        self.model = None
        self.device = "mps" if torch.backends.mps.is_available() else "cpu"

    def setup(self):
        """Model laden en voorbereiden"""
        print(f"🚀 DeepSeek-V3 Setup gestart...")
        print(f"📱 Device: {self.device}")

        try:
            # Tokenizer laden
            print("⏳ Tokenizer laden...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name, trust_remote_code=True
            )

            # Model laden (dit zal veel geheugen gebruiken!)
            print("⏳ Model laden... (Dit kan even duren)")
            print("⚠️  Waarschuwing: Dit model is 685GB groot!")

            # Voor macOS gebruiken we CPU/MPS in lagere precisie
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16 if self.device == "mps" else torch.float32,
                device_map="auto",
                trust_remote_code=True,
                low_cpu_mem_usage=True,
            )

            print("✅ Model succesvol geladen!")
            return True

        except Exception as e:
            print(f"❌ Fout bij laden model: {e}")
            print("\n💡 Mogelijke oplossingen:")
            print("1. Zorg dat je genoeg vrij geheugen hebt (>100GB)")
            print("2. Login bij Hugging Face: huggingface-cli login")
            print("3. Overweeg cloud deployment voor volledige functionaliteit")
            return False

    def chat(self, prompt, max_tokens=500, temperature=0.7):
        """Chat functie"""
        if not self.model or not self.tokenizer:
            print("❌ Model niet geladen. Run eerst setup()")
            return None

        # Input voorbereiden
        messages = [{"role": "user", "content": prompt}]
        text = self.tokenizer.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )

        # Tokenize
        inputs = self.tokenizer(text, return_tensors="pt")
        if self.device == "mps":
            inputs = {k: v.to("mps") for k, v in inputs.items()}

        # Generate
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=max_tokens,
                temperature=temperature,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
            )

        # Decode response
        response = self.tokenizer.decode(
            outputs[0][inputs["input_ids"].shape[1] :], skip_special_tokens=True
        )
        return response

    def demo(self):
        """Demo functie voor testen"""
        if not self.setup():
            return

        print("\n🎯 DeepSeek-V3 Demo gestart!")
        print("Typ 'quit' om te stoppen\n")

        while True:
            try:
                user_input = input("Leon: ")
                if user_input.lower() in ["quit", "exit", "stop"]:
                    break

                print("DeepSeek-V3: ", end="", flush=True)
                response = self.chat(user_input)
                print(response)
                print()

            except KeyboardInterrupt:
                print("\n👋 Demo gestopt!")
                break
            except Exception as e:
                print(f"❌ Fout: {e}")


def telegram_bot_example():
    """Voorbeeld voor Telegram bot integratie"""
    code = """
# Telegram Bot Integratie Voorbeeld
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters

deepseek = DeepSeekV3()
deepseek.setup()

async def handle_message(update: Update, context):
    user_message = update.message.text
    response = deepseek.chat(user_message)
    await update.message.reply_text(response)

# Bot setup
app = Application.builder().token("YOUR_BOT_TOKEN").build()
app.add_handler(MessageHandler(filters.TEXT, handle_message))
app.run_polling()
"""
    return code


if __name__ == "__main__":
    print("🤖 DeepSeek-V3 macOS Interface")
    print("=" * 40)

    # Opties tonen
    print("Kies een optie:")
    print("1. Demo starten")
    print("2. Telegram bot code tonen")
    print("3. Model info")

    choice = input("\nKeuze (1-3): ").strip()

    if choice == "1":
        ai = DeepSeekV3()
        ai.demo()
    elif choice == "2":
        print("\n📱 Telegram Bot Code:")
        print(telegram_bot_example())
    elif choice == "3":
        print("\n📊 DeepSeek-V3 Info:")
        print("• Model: 671B parameters (37B activated)")
        print("• Context: 128K tokens")
        print("• Prestaties: Beter dan GPT-4 op veel benchmarks")
        print("• Grootte: 685GB")
        print("• Hardware: 8x A100/H100 aanbevolen")
    else:
        print("❌ Ongeldige keuze")
